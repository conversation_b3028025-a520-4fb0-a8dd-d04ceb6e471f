import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/shared/presentation/widgets/responsive_layout.dart';

/// Admin login page with enhanced security features
class AdminLoginPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/login';

  const AdminLoginPage({super.key});

  @override
  ConsumerState<AdminLoginPage> createState() => _AdminLoginPageState();
}

class _AdminLoginPageState extends ConsumerState<AdminLoginPage> {
  final _formKey = GlobalKey<ShadFormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(adminAuthProvider);

    // Listen to auth state changes
    ref.listen<AdminAuthState>(adminAuthProvider, (previous, next) {
      if (next.isAuthenticated && !next.isLoading) {
        // Navigate to admin dashboard on successful login
        Navigator.of(context).pushReplacementNamed('/admin/dashboard');
      }
    });

    return Scaffold(
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(theme, authState),
        tablet: _buildTabletLayout(theme, authState),
        desktop: _buildDesktopLayout(theme, authState),
      ),
    );
  }

  Widget _buildMobileLayout(ShadThemeData theme, AdminAuthState authState) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildHeader(theme),
            const SizedBox(height: 32),
            _buildLoginForm(theme, authState),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout(ShadThemeData theme, AdminAuthState authState) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildHeader(theme),
            const SizedBox(height: 40),
            _buildLoginForm(theme, authState),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(ShadThemeData theme, AdminAuthState authState) {
    return Row(
      children: [
        // Left side - Branding
        Expanded(
          child: Container(
            color: theme.colorScheme.primary,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    lucide.LucideIcons.shield,
                    size: 80,
                    color: theme.colorScheme.primaryForeground,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    '3Pay Global',
                    style: theme.textTheme.h1.copyWith(
                      color: theme.colorScheme.primaryForeground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Admin Portal',
                    style: theme.textTheme.h3.copyWith(
                      color: theme.colorScheme.primaryForeground.withOpacity(
                        0.8,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // Right side - Login form
        Expanded(
          child: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 400),
              padding: const EdgeInsets.all(48.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildHeader(theme, showLogo: false),
                  const SizedBox(height: 40),
                  _buildLoginForm(theme, authState),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(ShadThemeData theme, {bool showLogo = true}) {
    return Column(
      children: [
        if (showLogo) ...[
          Icon(
            lucide.LucideIcons.shield,
            size: 64,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 16),
        ],
        Text(
          'Admin Sign In',
          style: theme.textTheme.h2,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Access the 3Pay Global administration portal',
          style: theme.textTheme.muted,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm(ShadThemeData theme, AdminAuthState authState) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: ShadForm(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Error message
              if (authState.error != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.destructive.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.destructive.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        LucideIcons.alertCircle,
                        size: 16,
                        color: theme.colorScheme.destructive,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          authState.error!,
                          style: TextStyle(
                            color: theme.colorScheme.destructive,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Session expired message
              if (authState.sessionExpired) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(LucideIcons.clock, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'Your session has expired. Please sign in again.',
                          style: TextStyle(color: Colors.orange, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Email field
              ShadInputFormField(
                id: 'admin_email',
                controller: _emailController,
                label: const Text('Email'),
                placeholder: const Text('Enter your admin email'),
                keyboardType: TextInputType.emailAddress,
                leading: const Padding(
                  padding: EdgeInsets.all(4.0),
                  child: Icon(lucide.LucideIcons.mail, size: 16),
                ),
                validator: (value) {
                  if (value.isEmpty) {
                    return 'Email is required';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Password field
              ShadInputFormField(
                id: 'admin_password',
                controller: _passwordController,
                label: const Text('Password'),
                placeholder: const Text('Enter your password'),
                obscureText: _obscurePassword,
                leading: const Padding(
                  padding: EdgeInsets.all(4.0),
                  child: Icon(lucide.LucideIcons.lock, size: 16),
                ),
                trailing: ShadIconButton.ghost(
                  icon: Icon(
                    _obscurePassword
                        ? lucide.LucideIcons.eyeOff
                        : lucide.LucideIcons.eye,
                    size: 16,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value.isEmpty) {
                    return 'Password is required';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Remember me checkbox
              Row(
                children: [
                  ShadCheckbox(
                    value: _rememberMe,
                    onChanged: (value) {
                      setState(() {
                        _rememberMe = value ?? false;
                      });
                    },
                  ),
                  const SizedBox(width: 8),
                  const Text('Remember me'),
                ],
              ),
              const SizedBox(height: 24),

              // Sign in button
              ShadButton(
                onPressed: authState.isLoading ? null : _handleSignIn,
                child:
                    authState.isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Text('Sign In'),
              ),
              const SizedBox(height: 16),

              // Security notice
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.muted.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      LucideIcons.info,
                      size: 16,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This is a secure admin portal. All activities are logged and monitored.',
                        style: theme.textTheme.small.copyWith(
                          color: theme.colorScheme.mutedForeground,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSignIn() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Clear any previous errors
    ref.read(adminAuthProvider.notifier).clearError();

    final email = _emailController.text.trim();
    final password = _passwordController.text;

    LoggerService.info('Admin login attempt for: $email');

    final success = await ref
        .read(adminAuthProvider.notifier)
        .signIn(email, password);

    if (success) {
      LoggerService.info('Admin login successful for: $email');
      // Navigation is handled by the listener in build method
    } else {
      LoggerService.warning('Admin login failed for: $email');
      // Error is already set in the provider
    }
  }
}
